* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

html,
body {
    width: 100%;
    height: 100%;
}

#product-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-top: 2rem;
    justify-content: center;
    align-items: flex-start;
    min-height: 50vh;
}

.card {
    width: 6vw;
    height: 15vh;
    border-radius: 5px;
}

.img-div {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 3rem;
    background-color: #222;
    color: #fff;
    border-radius: 0 0 1rem 1rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    font-family: 'Segoe UI', Arial, sans-serif;
    min-height: 60px;
}

nav h2 {
    font-size: 2rem;
    letter-spacing: 2px;
    font-weight: 700;
    margin: 0;
}

nav ul {
    display: flex;
    gap: 2.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

nav ul li a {
    color: #fff;
    text-decoration: none;
    font-size: 1.15rem;
    font-weight: 500;
    transition: color 0.2s;
}

nav ul li a:hover,
nav ul li a:focus {
    color: #eab308;
}