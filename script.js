const wrapper = document.querySelector("#wrapper")
const homeProducts = document.querySelector("#product-wrapper")
const card = document.querySelector("#card")

let url = "https://fakestoreapi.com/products"

async function fetchData() {
    let response = await fetch(url)
    let result = await response.json();
    console.log(result)
    console.log(result[0].title)

    for (let i = 0; i < result.length; i++) {
        // let card = document.querySelector("div")
        // card.classList.add("card")
        let image = document.createElement("img")
        let card = document.createElement("div")
        card.classList.add("product-card")
        card.append(image)
        image.classList.add("img-div")
        image.src = result[i].image
        homeProducts.append(card)
        // wrapper.append(card)
    }
}


fetchData();





























// let btn = document.querySelector("button")
// let input = document.querySelector("input")
// let h1 = document.querySelector("h1")
// input.focus();


// btn.addEventListener('click', function() {
//     let random = Math.floor(Math.random() * 11)
//     if(Number(input.value) === random){
//         h1.innerText = `You Win ${random}`
//     }else{
//         h1.innerText = `You Lose ${random}`
//     }
// })




